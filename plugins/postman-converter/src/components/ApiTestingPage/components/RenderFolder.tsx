import React from 'react';
import {
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Collapse,
} from '@material-ui/core';

// Icons
import FolderIcon from '@material-ui/icons/Folder';
import FolderOpenIcon from '@material-ui/icons/FolderOpen';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import ChevronRightIcon from '@material-ui/icons/ChevronRight';
import DescriptionIcon from '@material-ui/icons/Description';
import MoreVertIcon from '@material-ui/icons/MoreVert';

import { ApiCollection, ApiFolder } from '../../../types';
import { getMethodColor } from '../utils/requestUtils';

// RenderFolder component for rendering folders and their contents
interface RenderFolderProps {
  folder: ApiFolder;
  collection: ApiCollection;
  level: number;
  expandedFolders: Record<string, boolean>;
  selectedItemId: string | null;
  onFolderToggle: (folderId: string) => void;
  onItemSelect: (itemId: string) => void;
  onContextMenu: (event: React.MouseEvent, itemId: string, itemType: 'collection' | 'folder' | 'request') => void;
}

export const RenderFolder: React.FC<RenderFolderProps> = ({
  folder,
  collection,
  level,
  expandedFolders,
  selectedItemId,
  onFolderToggle,
  onItemSelect,
  onContextMenu,
}) => {
  return (
    <React.Fragment>
      <ListItem
        button
        style={{ paddingLeft: `${level * 16}px` }}
        onClick={() => onFolderToggle(folder.id)}
        onContextMenu={(e) => onContextMenu(e, folder.id, 'folder')}
      >
        <ListItemIcon>
          {expandedFolders[folder.id] ? (
            <FolderOpenIcon style={{ marginLeft: `${level * 8}px` }} />
          ) : (
            <FolderIcon style={{ marginLeft: `${level * 8}px` }} />
          )}
        </ListItemIcon>
        <ListItemText
          primary={folder.name}
          primaryTypographyProps={{
            style: {
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }
          }}
        />
        <IconButton
          edge="end"
          size="small"
          onClick={(e) => {
            e.stopPropagation();
            onContextMenu(e, folder.id, 'folder');
          }}
        >
          <MoreVertIcon />
        </IconButton>
      </ListItem>

      <Collapse in={expandedFolders[folder.id]} timeout="auto" unmountOnExit>
        {/* Render requests in this folder */}
        {folder.requests && folder.requests.map(requestId => {
          const request = collection.requests && collection.requests[requestId];
          if (!request) return null;

          return (
            <ListItem
              key={requestId}
              button
              style={{ paddingLeft: `${(level + 1) * 16}px` }}
              selected={selectedItemId === requestId}
              onClick={() => onItemSelect(requestId)}
              onContextMenu={(e) => onContextMenu(e, requestId, 'request')}
            >
              <ListItemIcon>
                <DescriptionIcon />
              </ListItemIcon>
              <ListItemText
                primary={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <span
                      style={{
                        backgroundColor: getMethodColor(request.method),
                        color: 'white',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        marginRight: '8px',
                        fontWeight: 'bold',
                        minWidth: '60px',
                        textAlign: 'center',
                        fontSize: '0.75rem',
                      }}
                    >
                      {request.method}
                    </span>
                    <span>{request.name}</span>
                  </div>
                }
                secondary={request.url}
                primaryTypographyProps={{
                  style: {
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }
                }}
                secondaryTypographyProps={{
                  style: {
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }
                }}
              />
              <IconButton
                edge="end"
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  onContextMenu(e, requestId, 'request');
                }}
              >
                <MoreVertIcon />
              </IconButton>
            </ListItem>
          );
        })}

        {/* Render nested folders */}
        {folder.folders && folder.folders.map(nestedFolder => (
          <RenderFolder
            key={nestedFolder.id}
            folder={nestedFolder}
            collection={collection}
            level={level + 1}
            expandedFolders={expandedFolders}
            selectedItemId={selectedItemId}
            onFolderToggle={onFolderToggle}
            onItemSelect={onItemSelect}
            onContextMenu={onContextMenu}
          />
        ))}
      </Collapse>
    </React.Fragment>
  );
};
