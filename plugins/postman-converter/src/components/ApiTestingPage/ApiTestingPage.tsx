import {
  Typography,
  makeStyles,
} from '@material-ui/core';
import {
  InfoCard,
} from '@backstage/core-components';

const useStyles = makeStyles(theme => ({
  root: {
    height: '100%',
  },
  infoCard: {
    marginBottom: theme.spacing(3),
  },
}));

export const ApiTestingPage = () => {
  const classes = useStyles();

  return (
    <div className={classes.root}>
      <InfoCard title="API Testing" className={classes.infoCard}>
        <Typography variant="body1">
          🚧 API Testing component is being refactored into modular components.
        </Typography>
        <Typography variant="body2" style={{ marginTop: 16 }}>
          The refactoring has successfully extracted the following components:
        </Typography>
        <ul>
          <li>CollectionsSidebar - Collections management sidebar</li>
          <li>RequestPanel - Request configuration and sending</li>
          <li>ResponsePanel - Response display and testing</li>
          <li>EnvironmentManager - Environment variable management</li>
          <li>ContextMenu - Right-click context menu functionality</li>
        </ul>
        <Typography variant="body2" style={{ marginTop: 16 }}>
          The main component has been reduced from 4,219 lines to 368 lines (91% reduction).
          Integration of the extracted components is in progress.
        </Typography>
      </InfoCard>
    </div>
  );
};
