import { useState, useCallback } from 'react';
import {
  makeStyles,
  Snackbar,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';

// Hooks
import { useCollections } from './hooks/useCollections';
import { useRequest } from './hooks/useRequest';
import { useEnvironments } from './hooks/useEnvironments';

// Components
import { CollectionsSidebar } from './components/CollectionsSidebar';
import { RequestPanel } from './components/RequestPanel';
import { ResponsePanel } from './components/ResponsePanel';
import { EnvironmentManager } from './components/EnvironmentManager';
import { ContextMenu } from './components/ContextMenu';
import { EnvironmentDialog } from './EnvironmentDialog';
import { ImportDialog } from './ImportDialog';
import { ExportDialog } from './ExportDialog';
// import { CreateFolderDialog } from './CreateFolderDialog';
// import { CreateRequestDialog } from './CreateRequestDialog';
// import { RenameDialog } from './components/RenameDialog';

// Types
import { ApiRequest, ApiEnvironment } from '../../types';

const useStyles = makeStyles(theme => ({
  root: {
    height: '100vh',
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
  },
  environmentBar: {
    padding: theme.spacing(1, 2),
    borderBottom: `1px solid ${theme.palette.divider}`,
    backgroundColor: theme.palette.background.paper,
  },
  mainContent: {
    flex: 1,
    display: 'flex',
    overflow: 'hidden',
  },
  sidebar: {
    width: 350,
    minWidth: 350,
    borderRight: `1px solid ${theme.palette.divider}`,
    backgroundColor: theme.palette.background.paper,
    overflow: 'hidden',
  },
  contentArea: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
  },
  requestPanel: {
    flex: 1,
    overflow: 'auto',
    padding: theme.spacing(2),
  },
  responsePanel: {
    flex: 1,
    overflow: 'auto',
    padding: theme.spacing(2),
  },
}));

export const ApiTestingPage = () => {
  const classes = useStyles();

  // Initialize hooks
  const collectionsHook = useCollections();
  const environmentsHook = useEnvironments();
  const requestHook = useRequest(collectionsHook.collections, collectionsHook.setCollections);

  // Dialog states
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
  // const [isCreateFolderDialogOpen, setIsCreateFolderDialogOpen] = useState(false);
  // const [isCreateRequestDialogOpen, setIsCreateRequestDialogOpen] = useState(false);
  // const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false);

  // Context menu state
  const [contextMenu, setContextMenu] = useState<{
    mouseX: number;
    mouseY: number;
    itemId: string;
    itemType: 'collection' | 'folder' | 'request';
  } | null>(null);

  // Rename dialog state
  // const [renameItem, setRenameItem] = useState<{
  //   id: string;
  //   type: 'collection' | 'folder' | 'request';
  //   currentName: string;
  //   collectionId?: string;
  // } | null>(null);

  // Create dialog states
  // const [createFolderData, setCreateFolderData] = useState<{
  //   collectionId: string;
  //   parentFolderId?: string;
  // } | null>(null);

  // const [createRequestData, setCreateRequestData] = useState<{
  //   collectionId: string;
  //   folderId?: string;
  // } | null>(null);

  // Snackbar state
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'info',
  });

  // Handle context menu
  const handleContextMenu = useCallback((
    event: React.MouseEvent,
    itemId: string,
    itemType: 'collection' | 'folder' | 'request'
  ) => {
    event.preventDefault();
    setContextMenu({
      mouseX: event.clientX - 2,
      mouseY: event.clientY - 4,
      itemId,
      itemType,
    });
  }, []);

  const handleContextMenuClose = useCallback(() => {
    setContextMenu(null);
  }, []);

  // Handle item selection
  const handleItemSelect = useCallback((itemId: string) => {
    collectionsHook.handleItemSelect(itemId);

    // If it's a request, load it into the request panel
    for (const collection of collectionsHook.collections) {
      if (collection.requests[itemId]) {
        requestHook.setCurrentRequest(collection.requests[itemId]);
        break;
      }
    }
  }, [collectionsHook, requestHook]);

  // Handle request updates
  const handleRequestUpdate = useCallback((updatedRequest: ApiRequest) => {
    requestHook.setCurrentRequest(updatedRequest);

    // Update the request in the collection
    const updatedCollections = collectionsHook.collections.map(collection => {
      if (collection.requests[updatedRequest.id]) {
        return {
          ...collection,
          requests: {
            ...collection.requests,
            [updatedRequest.id]: updatedRequest,
          },
        };
      }
      return collection;
    });

    collectionsHook.setCollections(updatedCollections);
  }, [collectionsHook, requestHook]);

  // Handle environment change
  const handleEnvironmentChange = useCallback((event: React.ChangeEvent<{ value: unknown }>) => {
    environmentsHook.setCurrentEnvironment(event.target.value as string);
  }, [environmentsHook]);

  // Show snackbar
  const showSnackbar = useCallback((message: string, severity: 'success' | 'error' | 'warning' | 'info' = 'info') => {
    setSnackbar({ open: true, message, severity });
  }, []);

  const handleSnackbarClose = useCallback(() => {
    setSnackbar(prev => ({ ...prev, open: false }));
  }, []);

  // Dialog handlers (temporarily disabled)
  const handleAddFolder = useCallback((collectionId: string, parentFolderId?: string) => {
    // setCreateFolderData({ collectionId, parentFolderId });
    // setIsCreateFolderDialogOpen(true);
    showSnackbar('Add folder functionality will be restored soon', 'info');
  }, [showSnackbar]);

  const handleAddRequest = useCallback((collectionId: string, folderId?: string) => {
    // setCreateRequestData({ collectionId, folderId });
    // setIsCreateRequestDialogOpen(true);
    showSnackbar('Add request functionality will be restored soon', 'info');
  }, [showSnackbar]);

  const handleRenameCollection = useCallback((collectionId: string, currentName: string) => {
    // setRenameItem({ id: collectionId, type: 'collection', currentName });
    // setIsRenameDialogOpen(true);
    showSnackbar('Rename functionality will be restored soon', 'info');
  }, [showSnackbar]);

  const handleRenameFolder = useCallback((collectionId: string, folderId: string, currentName: string) => {
    // setRenameItem({ id: folderId, type: 'folder', currentName, collectionId });
    // setIsRenameDialogOpen(true);
    showSnackbar('Rename functionality will be restored soon', 'info');
  }, [showSnackbar]);

  const handleRenameRequest = useCallback((collectionId: string, requestId: string, currentName: string) => {
    // setRenameItem({ id: requestId, type: 'request', currentName, collectionId });
    // setIsRenameDialogOpen(true);
    showSnackbar('Rename functionality will be restored soon', 'info');
  }, [showSnackbar]);

  const handleEditCollection = useCallback((collectionId: string) => {
    // Navigate to edit page or open edit dialog
    showSnackbar(`Edit collection ${collectionId}`, 'info');
  }, [showSnackbar]);

  const handleDuplicateRequest = useCallback((requestId: string) => {
    // Find and duplicate the request
    for (const collection of collectionsHook.collections) {
      if (collection.requests[requestId]) {
        const originalRequest = collection.requests[requestId];
        const duplicatedRequest = {
          ...originalRequest,
          id: `req_${Date.now()}`,
          name: `${originalRequest.name} Copy`,
        };

        const updatedCollection = {
          ...collection,
          requests: {
            ...collection.requests,
            [duplicatedRequest.id]: duplicatedRequest,
          },
        };

        const updatedCollections = collectionsHook.collections.map(c =>
          c.id === collection.id ? updatedCollection : c
        );

        collectionsHook.setCollections(updatedCollections);
        showSnackbar('Request duplicated successfully', 'success');
        break;
      }
    }
  }, [collectionsHook, showSnackbar]);

  const handleDeleteItem = useCallback((itemId: string, itemType: 'collection' | 'folder' | 'request') => {
    if (itemType === 'collection') {
      collectionsHook.handleDeleteCollection(itemId);
      showSnackbar('Collection deleted successfully', 'success');
    } else if (itemType === 'request') {
      // Remove request from collection
      const updatedCollections = collectionsHook.collections.map(collection => {
        if (collection.requests[itemId]) {
          const { [itemId]: removed, ...remainingRequests } = collection.requests;
          return { ...collection, requests: remainingRequests };
        }
        return collection;
      });
      collectionsHook.setCollections(updatedCollections);
      showSnackbar('Request deleted successfully', 'success');
    }
    // TODO: Handle folder deletion
  }, [collectionsHook, showSnackbar]);

  // Wrapper functions for specific delete handlers
  const handleDeleteCollection = useCallback((collectionId: string) => {
    handleDeleteItem(collectionId, 'collection');
  }, [handleDeleteItem]);

  const handleDeleteRequest = useCallback((requestId: string) => {
    handleDeleteItem(requestId, 'request');
  }, [handleDeleteItem]);

  const handleDeleteFolder = useCallback((folderId: string) => {
    handleDeleteItem(folderId, 'folder');
  }, [handleDeleteItem]);

  // Handle import collection
  const handleImportCollection = useCallback((collection: any) => {
    collectionsHook.handleImportCollection(collection);
    setIsImportDialogOpen(false);
    showSnackbar('Collection imported successfully', 'success');
  }, [collectionsHook, showSnackbar]);

  // Handle import environment
  const handleImportEnvironment = useCallback((environment: ApiEnvironment) => {
    environmentsHook.handleImportEnvironment(environment);
    setIsImportDialogOpen(false);
    showSnackbar('Environment imported successfully', 'success');
  }, [environmentsHook, showSnackbar]);

  // Get current environment for request panel
  const currentEnvironment = environmentsHook.environments.find(
    env => env.id === environmentsHook.currentEnvironment
  );

  return (
    <div className={classes.root}>
      {/* Environment Bar */}
      <div className={classes.environmentBar}>
        <EnvironmentManager
          environments={environmentsHook.environments}
          currentEnvironment={environmentsHook.currentEnvironment}
          selectedEnvironmentId={environmentsHook.currentEnvironment}
          environmentDialogOpen={environmentsHook.isEnvironmentDialogOpen}
          onEnvironmentChange={handleEnvironmentChange}
          onEnvironmentsUpdate={environmentsHook.setEnvironments}
          onSelectedEnvironmentChange={environmentsHook.setCurrentEnvironment}
          onDialogToggle={environmentsHook.setIsEnvironmentDialogOpen}
        />
      </div>

      {/* Main Content */}
      <div className={classes.mainContent}>
        {/* Sidebar */}
        <div className={classes.sidebar}>
          <CollectionsSidebar
            collections={collectionsHook.collections}
            collectionsLoading={collectionsHook.collectionsLoading}
            collectionsError={collectionsHook.collectionsError}
            expandedFolders={collectionsHook.expandedFolders}
            selectedItemId={collectionsHook.selectedItemId}
            onFolderToggle={collectionsHook.handleFolderToggle}
            onItemSelect={handleItemSelect}
            onAddCollection={collectionsHook.handleAddCollection}
            onDeleteCollection={handleDeleteCollection}
            onEditCollection={handleEditCollection}
            onAddFolder={handleAddFolder}
            onAddRequest={handleAddRequest}
            onDeleteRequest={handleDeleteRequest}
            onDeleteFolder={handleDeleteFolder}
            onDuplicateRequest={handleDuplicateRequest}
            onRenameCollection={handleRenameCollection}
            onRenameFolder={handleRenameFolder}
            onRenameRequest={handleRenameRequest}
          />
        </div>

        {/* Content Area */}
        <div className={classes.contentArea}>
          {/* Request Panel */}
          <div className={classes.requestPanel}>
            <RequestPanel
              request={requestHook.currentRequest}
              response={requestHook.currentResponse}
              isLoading={requestHook.isLoading}
              tabValue={requestHook.tabValue}
              onTabChange={requestHook.handleTabChange}
              onMethodChange={requestHook.handleMethodChange}
              onUrlChange={requestHook.handleUrlChange}
              onSendRequest={requestHook.handleSendRequest}
              onUpdateRequest={handleRequestUpdate}
              testResults={requestHook.testResults}
              isGeneratingTests={requestHook.isGeneratingTests}
              isRunningTests={requestHook.isRunningTests}
              testError={requestHook.testError}
              onGenerateTests={requestHook.handleGenerateTests}
              onRunTests={requestHook.handleRunTests}
              onSaveTests={requestHook.handleSaveTests}
              isSavingPreRequestScript={requestHook.isSavingPreRequestScript}
              preRequestScriptError={requestHook.preRequestScriptError}
              onSavePreRequestScript={requestHook.handleSavePreRequestScript}
              onSaveRequest={() => showSnackbar('Request saved', 'success')}
              currentEnvironment={currentEnvironment}
            />
          </div>

          {/* Response Panel */}
          <div className={classes.responsePanel}>
            {requestHook.currentResponse && (
              <ResponsePanel
                response={requestHook.currentResponse}
                tabValue={requestHook.responseTabValue}
                onTabChange={requestHook.handleResponseTabChange}
              />
            )}
          </div>
        </div>
      </div>

      {/* Context Menu */}
      <ContextMenu
        contextMenu={contextMenu}
        collections={collectionsHook.collections}
        onClose={handleContextMenuClose}
        onAddFolder={handleAddFolder}
        onAddRequest={handleAddRequest}
        onRenameCollection={handleRenameCollection}
        onRenameFolder={handleRenameFolder}
        onRenameRequest={handleRenameRequest}
        onEditCollection={handleEditCollection}
        onDuplicateRequest={handleDuplicateRequest}
        onDeleteItem={handleDeleteItem}
      />

      {/* Dialogs */}
      <EnvironmentDialog
        open={environmentsHook.isEnvironmentDialogOpen}
        environment={environmentsHook.environmentToEdit}
        onClose={() => environmentsHook.setIsEnvironmentDialogOpen(false)}
        onSave={environmentsHook.handleSaveEnvironment}
      />

      <ImportDialog
        open={isImportDialogOpen}
        onClose={() => setIsImportDialogOpen(false)}
        onImportCollection={handleImportCollection}
        onImportEnvironment={handleImportEnvironment}
      />

      <ExportDialog
        open={isExportDialogOpen}
        onClose={() => setIsExportDialogOpen(false)}
        collections={collectionsHook.collections}
        environments={environmentsHook.environments}
      />

      {/* TODO: Fix dialog interfaces */}
      {/*
      {createFolderData && (
        <CreateFolderDialog
          open={isCreateFolderDialogOpen}
          collectionId={createFolderData.collectionId}
          parentFolderId={createFolderData.parentFolderId}
          onClose={() => {
            setIsCreateFolderDialogOpen(false);
            setCreateFolderData(null);
          }}
          onSave={(folderName: string) => {
            collectionsHook.handleAddFolder(createFolderData.collectionId, folderName, createFolderData.parentFolderId);
            setIsCreateFolderDialogOpen(false);
            setCreateFolderData(null);
            showSnackbar('Folder created successfully', 'success');
          }}
        />
      )}

      {createRequestData && (
        <CreateRequestDialog
          open={isCreateRequestDialogOpen}
          collectionId={createRequestData.collectionId}
          folderId={createRequestData.folderId}
          onClose={() => {
            setIsCreateRequestDialogOpen(false);
            setCreateRequestData(null);
          }}
          onSave={(requestName: string, method: HttpMethod) => {
            collectionsHook.handleAddRequest(createRequestData.collectionId, requestName, method, createRequestData.folderId);
            setIsCreateRequestDialogOpen(false);
            setCreateRequestData(null);
            showSnackbar('Request created successfully', 'success');
          }}
        />
      )}

      {renameItem && (
        <RenameDialog
          open={isRenameDialogOpen}
          currentName={renameItem.currentName}
          itemType={renameItem.type}
          onClose={() => {
            setIsRenameDialogOpen(false);
            setRenameItem(null);
          }}
          onSave={(newName: string) => {
            if (renameItem.type === 'collection') {
              collectionsHook.handleRenameCollection(renameItem.id, newName);
            } else if (renameItem.type === 'folder' && renameItem.collectionId) {
              collectionsHook.handleRenameFolder(renameItem.collectionId, renameItem.id, newName);
            } else if (renameItem.type === 'request' && renameItem.collectionId) {
              collectionsHook.handleRenameRequest(renameItem.collectionId, renameItem.id, newName);
            }
            setIsRenameDialogOpen(false);
            setRenameItem(null);
            showSnackbar(`${renameItem.type} renamed successfully`, 'success');
          }}
        />
      )}
      */

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </div>
  );
};
